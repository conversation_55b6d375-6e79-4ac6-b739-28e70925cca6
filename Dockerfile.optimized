# Multi-stage Dockerfile for LegacyBridge MCP Server - Production Optimized
# Builds and runs the MCP server for web deployment

# Build arguments for versioning
ARG VERSION=unknown
ARG BUILD_DATE=unknown
ARG COMMIT_SHA=unknown

# Stage 1: Base dependencies layer (cached across builds)
FROM node:20-alpine AS base-deps
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Stage 2: Node.js dependency installer
FROM base-deps AS node-deps

WORKDIR /deps

# Copy package files only
COPY legacybridge/package*.json ./

# Install production dependencies only (cached if unchanged)
RUN npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# Stage 3: Node.js builder for MCP server
FROM base-deps AS node-builder

WORKDIR /build

# Copy dependencies from previous stage
COPY --from=node-deps /deps/node_modules ./node_modules

# Copy source files
COPY legacybridge/package*.json ./
COPY legacybridge/src ./src
COPY legacybridge/tsconfig.json ./
COPY legacybridge/scripts ./scripts

# Set build environment variables
ENV NODE_ENV=production

# Build TypeScript for MCP server
RUN npm run build:frontend && \
    # Create dist directory structure for MCP server
    mkdir -p dist/mcp-server && \
    # Copy built files
    cp -r src/mcp-server/* dist/mcp-server/ 2>/dev/null || true

# Stage 4: Security scanner
FROM aquasec/trivy:latest AS security-scanner
WORKDIR /scan
COPY --from=node-builder /build/package*.json ./
RUN trivy fs --no-progress --security-checks vuln .

# Stage 5: Final production image
FROM node:20-alpine AS production

# Install runtime dependencies only
RUN apk add --no-cache \
    libc6-compat \
    openssl \
    curl \
    && rm -rf /var/cache/apk/* \
    # Create non-root user
    && addgroup -g 1001 -S nodejs \
    && adduser -S mcpuser -u 1001

# Set working directory
WORKDIR /app

# Copy built artifacts with proper ownership
COPY --from=node-builder --chown=mcpuser:nodejs /build/dist ./dist
COPY --from=node-builder --chown=mcpuser:nodejs /build/node_modules ./node_modules
COPY --from=node-builder --chown=mcpuser:nodejs /build/package*.json ./
COPY --from=node-builder --chown=mcpuser:nodejs /build/scripts ./scripts

# Copy header files
COPY --chown=mcpuser:nodejs legacybridge/include/legacybridge.h ./include/

# Create necessary directories
RUN mkdir -p /app/logs /app/uploads /app/output /app/temp && \
    chown -R mcpuser:nodejs /app/logs /app/uploads /app/output /app/temp

# Set environment variables
ENV NODE_ENV=production
ENV MCP_PORT=3030
ENV LOG_LEVEL=info
ENV CACHE_ENABLED=false
ENV ENABLE_DOC=false
ENV ENABLE_WORDPERFECT=false

# Switch to non-root user
USER mcpuser

# Expose MCP server port
EXPOSE 3030

# Health check with proper timeout
HEALTHCHECK --interval=30s --timeout=3s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3030/health || exit 1

# Add metadata labels
LABEL org.opencontainers.image.title="LegacyBridge MCP Server" \
      org.opencontainers.image.description="Enterprise RTF-Markdown Converter MCP Server" \
      org.opencontainers.image.version="${VERSION}" \
      org.opencontainers.image.created="${BUILD_DATE}" \
      org.opencontainers.image.revision="${COMMIT_SHA}" \
      org.opencontainers.image.licenses="MIT" \
      org.opencontainers.image.vendor="LegacyBridge Team" \
      org.opencontainers.image.source="https://github.com/legacybridge/legacybridge"

# Start the MCP server
CMD ["node", "dist/mcp-server/index.js"]

# Stage 6: Debug image (optional, for troubleshooting)
FROM production AS debug
USER root
RUN apk add --no-cache \
    bash \
    vim \
    netcat-openbsd \
    procps \
    htop
USER mcpuser